<!-- 主題切換按鈕組件 -->
<template>
  <button
    @click="toggleTheme"
    :title="themeText"
    aria-label="切換主題"
    class="flex items-center gap-2 px-3 py-2 rounded-lg
           bg-coffee-light text-coffee-dark
           dark:bg-gray-700 dark:text-gray-200
           hover:bg-coffee hover:text-white
           dark:hover:bg-gray-600
           transition-all duration-200
           border border-coffee-light dark:border-gray-600
           focus:outline-none focus:ring-2 focus:ring-coffee focus:ring-opacity-50
           sm:px-3 sm:py-2 w-10 h-10 sm:w-auto sm:h-auto justify-center sm:justify-start"
  >
    <span class="text-lg">{{ themeIcon }}</span>
    <span class="text-sm font-medium hidden sm:inline">{{ themeText }}</span>
  </button>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

const { themeIcon, themeText, toggleTheme } = themeStore
</script>


<style scoped>


/* 響應式設計 - 小螢幕只顯示圖標 */

</style>
