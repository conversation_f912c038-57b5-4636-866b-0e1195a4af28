<template>
  <button
    @click="toggleTheme"
    class="theme-toggle-btn"
    :title="themeText"
    aria-label="切換主題"
  >
    <span class="theme-icon">{{ themeIcon }}</span>
    <span class="theme-text">{{ themeText }}</span>
  </button>
</template>

<script setup lang="ts">
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

const { themeIcon, themeText, toggleTheme } = themeStore
</script>

<style scoped>
.theme-toggle-btn {
  @apply flex items-center gap-2 px-3 py-2 rounded-lg
         bg-coffee-light dark:bg-gray-700 
         text-coffee-dark dark:text-gray-200
         hover:bg-coffee hover:text-white
         dark:hover:bg-gray-600
         transition-all duration-200
         border border-coffee-light dark:border-gray-600
         focus:outline-none focus:ring-2 focus:ring-coffee focus:ring-opacity-50;
}

.theme-icon {
  @apply text-lg;
}

.theme-text {
  @apply text-sm font-medium hidden sm:inline;
}

/* 響應式設計 - 小螢幕只顯示圖標 */
@media (max-width: 640px) {
  .theme-toggle-btn {
    @apply w-10 h-10 justify-center;
  }
  
  .theme-text {
    @apply hidden;
  }
}
</style>
