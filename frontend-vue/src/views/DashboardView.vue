<template>
  <div class="min-h-screen bg-coffee-light">
    <!-- 導航欄 -->
    <nav class="bg-coffee shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16 items-center">
          <h1 class="text-2xl font-bold text-indigo-700 flex items-center space-x-2">
            <span>☕</span>
            <span>Coffee Journal</span>
          </h1>
          <div class="flex items-center space-x-6">
            <span class="text-sm text-gray-600">👋 歡迎, <strong>{{ authStore.user?.name }}</strong></span>
            <button
              @click="handleLogout"
              class="px-4 py-1.5 bg-red-500 hover:bg-red-600 text-white rounded-full text-sm transition"
            >
              登出
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主內容 -->
    <main class="py-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white shadow rounded-xl p-8">
          <div class="text-center mb-10">
            <h2 class="text-3xl font-bold text-gray-800 mb-2">☕ 歡迎來到您的咖啡日記</h2>
            <p class="text-gray-500 text-base">記錄您的品嚐體驗，追蹤喜愛的咖啡豆與沖泡法</p>
          </div>

          <!-- 功能卡片區塊 -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 卡片組件 -->
            <DashboardCard
              title="咖啡筆記"
              icon="📝"
              description="管理您的咖啡品嚐記錄"
              color="indigo"
              @click="router.push('/notes')"
              buttonText="查看筆記"
            />
            <DashboardCard
              title="統計分析"
              icon="📊"
              description="查看您的品嚐統計"
              color="green"
              @click="loadStats"
              buttonText="查看統計"
            />
            <DashboardCard
              title="系統狀態"
              icon="⚡"
              description="檢查後端服務狀態"
              color="blue"
              @click="checkHealth"
              buttonText="檢查狀態"
            />
          </div>

          <!-- 狀態區塊 -->
          <div v-if="healthStatus" class="mt-8">
            <div class="flex items-center bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded">
              ✅ <span class="ml-2">Java 後端狀態：{{ healthStatus }}</span>
            </div>
          </div>
          <div v-if="error" class="mt-8">
            <div class="flex items-center bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded">
              ❌ <span class="ml-2">{{ error }}</span>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>


<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { healthApi } from '@/services/api'
import DashboardCard from '@/components/DashboardCard.vue'
const router = useRouter()
const authStore = useAuthStore()
const healthStatus = ref('')
const error = ref('')

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

const checkHealth = async () => {
  error.value = ''
  healthStatus.value = ''
  
  try {
    const response = await healthApi.checkJavaBackend()
    healthStatus.value = response.status || '正常運行'
  } catch (err: any) {
    error.value = '無法連接到 Java 後端服務'
    console.error('健康檢查失敗:', err)
  }
}

const loadStats = () => {
  // 未來可以實現統計功能
  alert('統計功能即將推出！')
}
</script>
