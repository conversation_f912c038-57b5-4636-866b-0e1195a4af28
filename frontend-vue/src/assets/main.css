@import './base.css';
@import "tailwindcss";

/* Coffee Journal 自訂樣式 */

/* 平滑滾動 */
html {
  scroll-behavior: smooth;
}

/* 自訂滾動條 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #7b96cb;
}

.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #d7ccc8;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background: #6f4e37;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 全域樣式增強 */
* {
  box-sizing: border-box;
}

/* 焦點樣式 */
:focus {
  outline: 2px solid #6f4e37;
  outline-offset: 2px;
}

.dark :focus {
  outline-color: #d7ccc8;
}

/* 選擇文字樣式 */
::selection {
  background-color: #d7ccc8;
  color: #95756f;
}

.dark ::selection {
  background-color: #6f4e37;
  color: #fefaf6;
}