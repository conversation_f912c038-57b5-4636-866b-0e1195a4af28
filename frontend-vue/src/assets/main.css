@import './base.css';
@import "tailwindcss";

/* Coffee Journal 自訂樣式 */

/* 按鈕樣式 */
.btn-primary {
  @apply bg-coffee hover:bg-coffee-dark text-white px-4 py-2 rounded-lg
         transition-colors duration-200 font-medium;
}

.btn-secondary {
  @apply bg-coffee-light hover:bg-coffee text-coffee-dark hover:text-white
         px-4 py-2 rounded-lg transition-colors duration-200 font-medium;
}

.btn-danger {
  @apply bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg
         transition-colors duration-200 font-medium;
}

.btn-outline {
  @apply border-2 border-coffee text-coffee hover:bg-coffee hover:text-white
         px-4 py-2 rounded-lg transition-colors duration-200 font-medium;
}

/* 卡片樣式 */
.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-6
         border border-gray-200 dark:border-gray-700;
}

.card-hover {
  @apply card hover:shadow-lg transition-shadow duration-200;
}

.card-coffee {
  @apply bg-cream dark:bg-gray-800 border-coffee-light dark:border-gray-600
         rounded-lg p-6 shadow-sm;
}

/* 表單樣式 */
.input {
  @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600
         rounded-lg focus:ring-2 focus:ring-coffee focus:border-coffee
         bg-white dark:bg-gray-700 text-coffee-dark dark:text-gray-100;
}

.label {
  @apply block text-sm font-medium text-coffee-dark dark:text-gray-200 mb-2;
}

.error-message {
  @apply text-red-500 text-sm mt-1;
}

/* 響應式容器 */
.responsive-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.responsive-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

/* 動畫效果 */
.transition-standard {
  @apply transition-all duration-200 ease-in-out;
}

.hover-lift {
  @apply transform hover:-translate-y-1 transition-transform duration-200;
}

.hover-scale {
  @apply transform hover:scale-105 transition-transform duration-200;
}

/* 頁面標題 */
.page-title {
  @apply text-3xl font-bold text-coffee-dark dark:text-cream mb-4 font-serif;
}

.section-title {
  @apply text-xl font-semibold text-coffee-dark dark:text-cream mb-2;
}

/* 平滑滾動 */
html {
  scroll-behavior: smooth;
}

/* 自訂滾動條 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-coffee-light dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-coffee dark:bg-gray-500;
}