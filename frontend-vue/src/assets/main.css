@import './base.css';
@import "tailwindcss";

/* 按鈕統一樣式 */
.btn {
  border-radius: 9999px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.btn-coffee {
  background-color: #6f4e37;
  color: white;
}

.btn-coffee:hover {
  background-color: #3e2723;
}

.btn-outline {
  border: 1px solid #6f4e37;
  color: #6f4e37;
  background-color: transparent;
}

.btn-outline:hover {
  background-color: #d7ccc8;
  color: white;
}

/* 卡片統一樣式 */
.card {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: box-shadow 0.2s;
}

.dark .card {
  background-color: #1f1f1f;
}

.card:hover {
  box-shadow: 0 4px 6px rgba(0,0,0,0.15);
}

/* 響應式容器 */
.container {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* 暗模式 */
.dark body {
  background-color: #1f1f1f;
  color: #feb164f4;
}

/* 頁面標題 */
.page-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #6f4e37;
}

/* 區塊標題 */
.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #3e2723;
}

/* 響應式字體大小調整 */
.text-responsive {
  font-size: 1rem;
}

@media (min-width: 640px) {
  .text-responsive {
    font-size: 1.125rem;
  }
}

@media (min-width: 768px) {
  .text-responsive {
    font-size: 1.25rem;
  }
}